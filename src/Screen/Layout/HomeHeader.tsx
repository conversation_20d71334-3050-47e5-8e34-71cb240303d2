import React, {useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Platform,
  Image,
  Dimensions,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {AppSvg, ListTile, Winicon} from 'wini-mobile-components';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../router/router';
import LinearGradient from 'react-native-linear-gradient';
import iconSvg from '../../svg/icon';
import CartIcon from '../../components/CartIcon';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {ColorThemes} from '../../assets/skin/colors';
import ConfigAPI from '../../Config/ConfigAPI';
import Svg, {Path} from 'react-native-svg';
import ScreenHeader from './header';

const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;
const SCREEN_HEIGHT = Dimensions.get('window').height;

interface HomeHeaderProps {
  onSearchPress?: () => void;
  notificationCount?: number;
}

const HomeHeader: React.FC<HomeHeaderProps> = ({
  onSearchPress,
  notificationCount = 5,
}) => {
  const navigation = useNavigation<any>();

  // Thiết lập StatusBar khi component mount
  useEffect(() => {
    // Đảm bảo StatusBar trong suốt và kịch lên đỉnh màn hình
    if (Platform.OS === 'android') {
      StatusBar.setTranslucent(true);
      StatusBar.setBackgroundColor('transparent');
    }
    StatusBar.setBarStyle('dark-content');

    // Trả về hàm cleanup để reset StatusBar khi unmount
    return () => {
      if (Platform.OS === 'android') {
        StatusBar.setTranslucent(false);
        StatusBar.setBackgroundColor('#FFA500');
      }
    };
  }, []);

  const customer = useSelectorCustomerState().data;

  return (
    <View style={styles.header}>
      {/* Background màu xanh */}
      <View style={styles.headerBackground}>
        <Svg
          width={Dimensions.get('window').width}
          height={140}
          viewBox={`0 0 ${Dimensions.get('window').width} 140`}>
          <Path
            d="
                       M0,50
                       C110,0 220,100 330,50
                       C385,25 412,50 440,50
                       L440,180
                       L0,180 Z"
            fill="#84DEEA"
          />
          {/* Nền xanh dương phía trên */}
          <Path
            d="
                       M0,0 H440 V50
                       C412,50 385,25 330,50
                       C220,100 110,0 0,50 Z"
            fill="#1A30FF"
          />
        </Svg>
      </View>

      {/* Nội dung header */}
      <SafeAreaView style={styles.headerContent}>
        <ListTile
          leading={
            <View
              style={{
                width: 40,
                height: 40,
                borderRadius: 100,
                overflow: 'hidden',
              }}>
              <Image
                source={{
                  uri: customer?.AvatarUrl
                    ? `${ConfigAPI.urlImg + customer?.AvatarUrl}`
                    : 'https://placehold.co/40/FFFFFF/000000/png',
                }}
                style={{width: '100%', height: '100%', borderRadius: 100}}
              />
            </View>
          }
          title={`Xin chào, ${customer?.Name}`}
          style={{
            width: '100%',
            padding: 0,
            paddingHorizontal: 16,
            backgroundColor: ColorThemes.light.transparent,
          }}
          trailing={
            <View style={styles.rightIcons}>
              {/* Cart icon */}

              {/* User profile icon */}
              <TouchableOpacity style={styles.iconButton} onPress={() => {}}>
                <View style={styles.iconCircle}>
                  <AppSvg SvgSrc={iconSvg.notification} size={16} />
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.iconButton}
                onPress={() => navigate(RootScreen.CartPage)}>
                <View style={styles.iconCircle}>
                  <CartIcon isHome color="#0033CC" size={18} showBadge={true} />
                </View>
              </TouchableOpacity>
              {/* Notification icon */}
              {/* <TouchableOpacity
              style={styles.iconButton}
              onPress={() => navigate(RootScreen.Notification)}>
              <View style={styles.iconCircle}>
                <AppSvg SvgSrc={iconSvg.card} size={16} />
              </View>
            </TouchableOpacity> */}
            </View>
          }
          bottom={
            <View style={styles.searchContainer}>
              <TouchableOpacity
                style={styles.searchBar}
                onPress={onSearchPress}
                activeOpacity={0.8}>
                <Winicon
                  src="outline/user interface/search"
                  size={18}
                  color="#999"
                />
                <Text style={styles.searchPlaceholder}>Bạn muốn tìm gì?</Text>
              </TouchableOpacity>
              {/* <TouchableOpacity style={styles.filterButton}>
                <AppSvg SvgSrc={iconSvg.filter} size={24} />
              </TouchableOpacity> */}
            </View>
          }
        />
      </SafeAreaView>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* StatusBar đã được thiết lập trong useEffect */}
      {/* Background gradient */}
      <LinearGradient
        colors={['#FFA500', '#87CEEB']}
        start={{x: 0, y: 0}}
        end={{x: 1, y: 0}}
        style={styles.gradientBackground}>
        {/* Wavy bottom border */}
        <View style={styles.wavyBottom}>
          <View style={styles.wavyCurve} />
        </View>
      </LinearGradient>

      {/* Header content */}
      <View style={styles.headerContent}>
        <View style={styles.topRow}>
          {/* Menu button */}
          {/* <TouchableOpacity
            style={styles.menuButton}
            onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}>
            <AppSvg SvgSrc={iconSvg.menu} size={24} />
          </TouchableOpacity> */}

          {/* Logo */}
          <View style={styles.logoContainer}>
            {/* <Image source={require('../../assets/logo.png')} /> */}
            <ListTile
              leading={
                <View
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 100,
                    overflow: 'hidden',
                  }}>
                  <Image
                    source={{
                      uri: customer?.Img
                        ? `${ConfigAPI.urlImg + customer?.Img}`
                        : 'https://placehold.co/40/FFFFFF/000000/png',
                    }}
                    style={{width: '100%', height: '100%', borderRadius: 100}}
                  />
                </View>
              }
              title={`Xin chào, ${customer?.Name}`}
              style={{
                padding: 0,
                backgroundColor: ColorThemes.light.transparent,
              }}
            />
          </View>
          {/* Right icons */}
          <View style={styles.rightIcons}>
            {/* Cart icon */}

            {/* User profile icon */}
            <TouchableOpacity style={styles.iconButton} onPress={() => {}}>
              <View style={styles.iconCircle}>
                <AppSvg SvgSrc={iconSvg.notification} size={16} />
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => navigate(RootScreen.CartPage)}>
              <View style={styles.iconCircle}>
                <CartIcon isHome color="#0033CC" size={18} showBadge={true} />
              </View>
            </TouchableOpacity>
            {/* Notification icon */}
            {/* <TouchableOpacity
              style={styles.iconButton}
              onPress={() => navigate(RootScreen.Notification)}>
              <View style={styles.iconCircle}>
                <AppSvg SvgSrc={iconSvg.card} size={16} />
              </View>
            </TouchableOpacity> */}
          </View>
        </View>

        {/* Search bar with filter icon */}
        <View style={styles.searchContainer}>
          <TouchableOpacity
            style={styles.searchBar}
            onPress={onSearchPress}
            activeOpacity={0.8}>
            <Winicon
              src="outline/user interface/search"
              size={18}
              color="#999"
            />
            <Text style={styles.searchPlaceholder}>Bạn muốn tìm gì?</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.filterButton}>
            {/* <Winicon src="fill/user interface/filter-check" size={18} color="#FFA500" /> */}
            <AppSvg SvgSrc={iconSvg.filter} size={24} />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    position: 'relative',
    paddingTop: 0,
    marginTop: 0,
  },

  header: {
    width: '100%',
    position: 'relative',
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  waveContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 2,
    overflow: 'hidden',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    top: -6,
    zIndex: 2,
    left: 0,
    right: 0,
  },

  gradientBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  wavyBottom: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 30,
    overflow: 'hidden',
  },
  wavyCurve: {
    position: 'absolute',
    bottom: -30,
    left: 0,
    right: 0,
    height: 60,
    backgroundColor: 'white',
    borderTopLeftRadius: 100,
    borderTopRightRadius: 100,
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  menuButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    flex: 1,
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#0033CC',
  },
  logoHighlight: {
    backgroundColor: '#0033CC',
    color: 'white',
    borderRadius: 4,
    overflow: 'hidden',
    paddingHorizontal: 2,
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 8,
  },
  iconCircle: {
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: 'red',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 11,
    paddingHorizontal: 16,
    paddingVertical: 8,
    height: 36,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,
  },
  searchPlaceholder: {
    flex: 1,
    marginLeft: 8,
    color: '#999',
    fontSize: 14,
  },
  filterButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default HomeHeader;
