import React, {useState, useEffect, useCallback, useMemo, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  TextInput,
  Dimensions,
  ScrollView,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useNavigation, useRoute} from '@react-navigation/native';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {ProductDA, ProductItem} from '../productDA';
import categoryDA from '../../category/categoryDA';
import ProductCard from '../card/ProductCard';
import {RootScreen} from '../../../router/router';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import CartIcon from '../../../components/CartIcon';
import ConfigAPI from '../../../Config/ConfigAPI';
import HeaderProductByCate from '../../../Screen/Layout/headerProdByCate';
import CategoryGrid from '../../category/CategoryGrid';
import FastImage from 'react-native-fast-image';

const {width} = Dimensions.get('window');

// Tab data for filters
const filterTabs = [
  {id: 'hot', name: 'HOT', icon: 'fill/development/shape-star'},
  {id: 'freeship', name: 'Freeship', icon: 'fill/shopping/delivery'},
  {id: 'new', name: 'Mới', icon: 'fill/arrows/reload'},
  {id: 'discount', name: 'Nhận hàng ưu đãi', icon: 'fill/shopping/tag'},
];

interface Category {
  Id: string;
  Name: string;
  Img: string;
}

export default function ProductListByCategory() {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const {categoryId, categoryName} = route.params || {};

  // State management
  const [cateName, setCateName] = useState(categoryName || '');
  const [products, setProducts] = useState<ProductItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>(
    categoryId || '',
  );
  const [selectedFilter, setSelectedFilter] = useState<string>('hot');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Initialize data access objects
  const productDA = useMemo(() => new ProductDA(), []);
  const categoryDataAccess = useMemo(() => new categoryDA(), []);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      const result = await categoryDataAccess.getAll();
      if (result && result.data) {
        setCategories(result.data);
        if (!selectedCategory && result.data.length > 0) {
          setSelectedCategory(result.data[0].Id);
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }, [categoryDataAccess, selectedCategory]);

  // Fetch products based on selected category and filter
  const fetchProducts = useCallback(
    async (reset = false) => {
      try {
        if (reset) {
          setIsLoading(true);
          setPage(1);
        }

        const currentPage = reset ? 1 : page;
        let result = null;

        // Apply different logic based on selected filter
        switch (selectedFilter) {
          case 'hot':
            result = await productDA.getProductBestSeller(currentPage, 20);
            break;
          case 'freeship':
            // Filter products with free shipping
            result = await productDA.getAllListbyCategory(
              currentPage,
              20,
              selectedCategory,
            );
            break;
          case 'new':
            result = await productDA.getAllListbyCategory(
              currentPage,
              20,
              selectedCategory,
            );
            break;
          case 'discount':
            // Filter products with discount
            result = await productDA.getAllListbyCategory(
              currentPage,
              20,
              selectedCategory,
            );
            break;
          default:
            result = await productDA.getAllListbyCategory(
              currentPage,
              20,
              selectedCategory,
            );
        }

        if (result && result.data) {
          if (reset) {
            setProducts(result.data);
          } else {
            setProducts(prev => [...prev, ...result.data]);
          }
          setHasMore(result.data.length === 20);
          setPage(currentPage + 1);
        }
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setIsLoading(false);
        setIsRefreshing(false);
      }
    },
    [productDA, selectedCategory, selectedFilter, page],
  );

  // Initial data fetch
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  useEffect(() => {
    if (selectedCategory) {
      fetchProducts(true);
    }
  }, [selectedCategory, selectedFilter]);

  // Handle refresh
  const onRefresh = useCallback(() => {
    setIsRefreshing(true);
    fetchProducts(true);
  }, [fetchProducts]);

  // Handle load more
  const onLoadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      fetchProducts(false);
    }
  }, [isLoading, hasMore, fetchProducts]);

  // Handle category selection
  const handleCategorySelect = useCallback((categoryId: string) => {
    // scrollviewRef current scroll to category
    scrollviewRef.current?.scrollTo({
      x: categories.findIndex(category => category.Id === categoryId) * 100,
      animated: true,
    });
    setSelectedCategory(categoryId);
  }, []);

  // Handle filter selection
  const handleFilterSelect = useCallback((filterId: string) => {
    setSelectedFilter(filterId);
  }, []);

  // Handle product press
  const handleProductPress = useCallback(
    (product: ProductItem) => {
      navigation.push(RootScreen.ProductDetail, {id: product.Id});
    },
    [navigation],
  );

  // Render header
  const renderHeader = () => (
    <HeaderProductByCate title={cateName || 'Danh mục sản phẩm'} />
  );

  // Render filter tabs
  const renderFilterTabs = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.filterTabsContainer}
      contentContainerStyle={styles.filterTabsContent}>
      {filterTabs.map(tab => (
        <TouchableOpacity
          key={tab.id}
          style={[
            styles.filterTab,
            selectedFilter === tab.id && styles.activeFilterTab,
          ]}
          onPress={() => handleFilterSelect(tab.id)}>
          <Winicon
            src={tab.icon}
            size={16}
            color={selectedFilter === tab.id ? '#FFFFFF' : '#666666'}
          />
          <Text
            style={[
              styles.filterTabText,
              selectedFilter === tab.id && styles.activeFilterTabText,
            ]}>
            {tab.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const scrollviewRef = useRef<any>(null);

  // Render category tabs
  const renderCategoryTabs = () => (
    <ScrollView
      ref={scrollviewRef}
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.categoryTabsContainer}
      contentContainerStyle={styles.categoryTabsContent}>
      {categories.length == 0 ? (
        <SkeletonPlaceholder backgroundColor="#F0F0F0" highlightColor="#E0E0E0">
          <View style={{flexDirection: 'row', gap: 12}}>
            {Array(5)
              .fill(0)
              .map((_, index) => (
                <View key={index} style={{alignItems: 'center', gap: 4}}>
                  <SkeletonPlaceholder.Item
                    width={30}
                    height={30}
                    borderRadius={15}
                  />
                  <SkeletonPlaceholder.Item
                    width={60}
                    height={12}
                    borderRadius={4}
                  />
                </View>
              ))}
          </View>
        </SkeletonPlaceholder>
      ) : (
        categories.map(category => (
          <TouchableOpacity
            key={category.Id}
            style={[
              styles.categoryTab,
              selectedCategory === category.Id && styles.activeCategoryTab,
            ]}
            onPress={() => {
              handleCategorySelect(category.Id);
              setCateName(category.Name);
            }}>
            <FastImage
              key={category.Img}
              source={{uri: ConfigAPI.urlImg + category.Img}}
              style={{width: 30, height: 30}}
              resizeMode={FastImage.resizeMode.contain}
            />
            <Text
              style={[
                styles.categoryTabText,
                selectedCategory === category.Id &&
                  styles.activeCategoryTabText,
              ]}>
              {category.Name}
            </Text>
          </TouchableOpacity>
        ))
      )}
    </ScrollView>
  );

  // Render product skeleton
  const renderProductSkeleton = () => (
    <View style={styles.skeletonContainer}>
      {Array(6)
        .fill(0)
        .map((_, index) => (
          <View key={index} style={styles.skeletonItem}>
            <SkeletonPlaceholder
              backgroundColor="#F0F0F0"
              highlightColor="#E0E0E0">
              <SkeletonPlaceholder.Item
                width="100%"
                height={200}
                borderRadius={8}
              />
              <SkeletonPlaceholder.Item
                width="80%"
                height={16}
                borderRadius={4}
                marginTop={8}
              />
              <SkeletonPlaceholder.Item
                width="60%"
                height={14}
                borderRadius={4}
                marginTop={4}
              />
            </SkeletonPlaceholder>
          </View>
        ))}
    </View>
  );

  // Render product item
  const renderProductItem = ({item}: {item: ProductItem}) => (
    <ProductCard
      item={item}
      onPress={handleProductPress}
      width={(width - 48) / 2}
      height={((width - 48) / 2) * 1.8}
    />
  );

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      {renderHeader()}
      {renderFilterTabs()}
      {renderCategoryTabs()}
      <FlatList
        data={products}
        renderItem={renderProductItem}
        keyExtractor={(item, index) => item.Id || index.toString()}
        numColumns={2}
        contentContainerStyle={styles.productList}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        ListEmptyComponent={() => {
          if (isLoading) {
            Array(6)
              .fill(0)
              .map((_, index) => (
                <View key={index} style={styles.skeletonItem}>
                  <SkeletonPlaceholder
                    backgroundColor="#F0F0F0"
                    highlightColor="#E0E0E0">
                    <SkeletonPlaceholder.Item
                      width="100%"
                      height={200}
                      borderRadius={8}
                    />
                    <SkeletonPlaceholder.Item
                      width="80%"
                      height={16}
                      borderRadius={4}
                      marginTop={8}
                    />
                    <SkeletonPlaceholder.Item
                      width="60%"
                      height={14}
                      borderRadius={4}
                      marginTop={4}
                    />
                  </SkeletonPlaceholder>
                </View>
              ));
          }
          return <Text style={{color: '#000000'}}>Không có dữ liệu</Text>;
        }}
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },

  // Filter tabs styles
  filterTabsContainer: {
    backgroundColor: '#FFFFFF',
    maxHeight: 36,
    flex: 1,
  },
  filterTabsContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  filterTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    backgroundColor: '#F5F5F5',
    borderRadius: 20,
    gap: 6,
  },
  activeFilterTab: {
    backgroundColor: '#2962FF',
  },
  filterTabText: {
    ...TypoSkin.buttonText6,
  },
  activeFilterTabText: {
    color: ColorThemes.light.neutral_absolute_background_color,
  },
  // Category tabs styles
  categoryTabsContainer: {
    maxHeight: 73,
    marginTop: 16,
  },
  categoryTabsContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 4,
    gap: 4,
    borderRadius: 8,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0.5,
    borderColor: '#4EC6EA',
    overflow: 'hidden',
  },
  activeCategoryTab: {
    backgroundColor: '#E3F2FD',
  },
  categoryTabText: {
    ...TypoSkin.buttonText6,
  },
  activeCategoryTabText: {
    color: '#2962FF',
  },
  // Product list styles
  productList: {
    padding: 16,
    gap: 16,
  },
  // Skeleton styles
  skeletonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    gap: 16,
  },
  skeletonItem: {
    width: (width - 48) / 2,
  },
});
