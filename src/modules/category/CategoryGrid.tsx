import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  FlatList,
  Image,
  ViewStyle,
} from 'react-native';

import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {navigate, RootScreen} from '../../router/router';
import {TypoSkin} from '../../assets/skin/typography';
import {ColorThemes} from '../../assets/skin/colors';
import categoryDA from './categoryDA';
import ConfigAPI from '../../Config/ConfigAPI';
import FastImage from 'react-native-fast-image';
const {width} = Dimensions.get('window');

// Định nghĩa kiểu dữ liệu cho danh mục
interface Category {
  Id: string;
  Name: string;
  Img: string;
  route?: string; // Đường dẫn điều hướng khi nhấn vào danh mục
  color?: string; // <PERSON>àu nền cho icon (tùy chọn)
  badge?: string; // Badge hiển thị trên danh mục (ví dụ: "Sale")
}

interface CategoryGridProps {
  title?: string;
  categories?: Category[];
  numColumns?: number;
  onCategoryPress?: (category: Category) => void;
  horizontal?: boolean;
  style?: ViewStyle;
  styleItem?: ViewStyle;
}

const CategoryGrid: React.FC<CategoryGridProps> = ({
  title,
  categories = [],
  numColumns = 3,
  onCategoryPress,
  style,
  horizontal = false,
  styleItem,
}) => {
  // Tính toán kích thước của mỗi item dựa trên số cột
  const itemWidth = (width - 48 - 16) / numColumns; // 48 = padding left + right + gap giữa các cột

  const [data, setData] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const da = new categoryDA();
      const result = await da.getAll();

      if (result && result.data) {
        setData(result.data);
      } else {
        setError('Không thể tải dữ liệu danh mục');
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError('Đã xảy ra lỗi khi tải dữ liệu');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCategoryPress = (category: Category) => {
    if (onCategoryPress) {
      onCategoryPress(category);
    }
  };

  const renderItem = ({item}: {item: Category}) => (
    <TouchableOpacity
      style={[{width: itemWidth, ...styleItem}]}
      onPress={() => handleCategoryPress(item)}
      activeOpacity={0.7}>
      <View style={styles.categoryContent}>
        <View style={[styles.iconContainer]}>
          <FastImage
            key={item.Img}
            source={{uri: ConfigAPI.urlImg + item.Img}}
            style={{width: 30, height: 30}}
            resizeMode={FastImage.resizeMode.contain}
          />
        </View>
        <Text style={styles.categoryName} numberOfLines={2}>
          {item.Name}
        </Text>
        {item.badge && (
          <View style={styles.badgeContainer}>
            <Text style={styles.badgeText}>{item.badge}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  // Render skeleton loading placeholders
  const renderShimmerItem = () => (
    <View style={[styles.categoryItem, {width: itemWidth}]}>
      <View style={styles.categoryContent}>
        <SkeletonPlaceholder backgroundColor="#F0F0F0" highlightColor="#E0E0E0">
          <SkeletonPlaceholder.Item
            width={60}
            height={60}
            borderRadius={30}
            marginBottom={12}
          />
          <SkeletonPlaceholder.Item width="80%" height={12} borderRadius={4} />
        </SkeletonPlaceholder>
      </View>
    </View>
  );

  // Tạo mảng dữ liệu giả cho shimmer effect
  const shimmerData = Array(6)
    .fill(0)
    .map((_, index) => ({id: `shimmer-${index}`}));

  return (
    <View style={styles.container}>
      {title && <Text style={styles.title}>{title}</Text>}

      {isLoading ? (
        <FlatList
          horizontal={horizontal ?? false}
          style={style}
          data={shimmerData}
          renderItem={renderShimmerItem}
          keyExtractor={item => item.id}
          numColumns={horizontal ? 1 : numColumns}
          scrollEnabled={false}
          contentContainerStyle={styles.gridContainer}
          columnWrapperStyle={{gap: 16}}
        />
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={fetchCategories}>
            <Text style={styles.retryText}>Thử lại</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          horizontal={horizontal ?? false}
          style={style}
          data={data.length > 0 ? data : categories}
          renderItem={renderItem}
          keyExtractor={item => item.Id}
          numColumns={horizontal ? 1 : numColumns}
          scrollEnabled={false}
          contentContainerStyle={styles.gridContainer}
          columnWrapperStyle={{gap: 16}}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
    paddingHorizontal: 16,
  },
  title: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
    marginBottom: 12,
  },
  gridContainer: {
    paddingVertical: 8,
    marginVertical: 8,
    gap: 16,
  },
  categoryItem: {
    paddingHorizontal: 8,
  },
  categoryContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0.5,
    borderColor: '#4EC6EA',
    height: 80,
    overflow: 'hidden',
    gap: 8,
  },
  iconContainer: {
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryName: {
    lineHeight: 12,
    fontSize: 11,
    color: ColorThemes.light.neutral_text_title_color,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  badgeContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#8E24AA',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },

  // Styles for error state
  errorContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF3F3',
    borderRadius: 8,
    marginVertical: 8,
  },
  errorText: {
    color: '#D32F2F',
    marginBottom: 12,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: ColorThemes.light.primary_main_color,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  retryText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default CategoryGrid;
